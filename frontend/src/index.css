@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-blue text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-secondary-blue text-white px-4 py-2 rounded-lg hover:bg-cyan-400 transition-colors duration-200;
  }

  .btn-success {
    @apply bg-primary-green text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-200;
  }
  
  .btn-outline {
    @apply border border-primary-blue text-primary-blue px-4 py-2 rounded-lg hover:bg-primary-blue hover:text-white transition-colors duration-200;
  }

  .btn-outline-success {
    @apply border border-primary-green text-primary-green px-4 py-2 rounded-lg hover:bg-primary-green hover:text-white transition-colors duration-200;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-4 border border-gray-200;
  }
  
  .post-card {
    @apply bg-white rounded-lg shadow-sm p-4 border border-gray-200 hover:shadow-md transition-shadow duration-200;
  }
  
  .sidebar {
    @apply bg-light-gray border-r border-gray-200 h-screen sticky top-0;
  }
  
  .main-content {
    @apply flex-1 max-w-2xl mx-auto;
  }
  
  .right-sidebar {
    @apply bg-light-gray border-l border-gray-200 h-screen sticky top-0;
  }
}

/* Dark mode styles */
.dark {
  @apply bg-gray-900 text-white;
}

.dark .card {
  @apply bg-gray-800 border-gray-700;
}

.dark .post-card {
  @apply bg-gray-800 border-gray-700 hover:bg-gray-700;
}

.dark .sidebar {
  @apply bg-gray-800 border-gray-700;
}

.dark .right-sidebar {
  @apply bg-gray-800 border-gray-700;
}

.dark .input-field {
  @apply bg-gray-700 border-gray-600 text-white placeholder-gray-400;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Conversation tree styles */
.conversation-tree {
  border-left: 2px solid #e5e7eb;
  margin-left: 1rem;
  padding-left: 1rem;
}

.conversation-branch {
  border-left: 2px solid #093FB4;
  margin-left: 1rem;
  padding-left: 1rem;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #093FB4;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notification badge */
.notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .sidebar {
    @apply hidden;
  }
  
  .right-sidebar {
    @apply hidden;
  }
  
  .main-content {
    @apply max-w-full px-4;
  }
}
