import requests
import json
import hashlib
from flask import current_app
from datetime import datetime, timedelta
import logging
import time

class TranslationService:

    def __init__(self):
        self.ollama_host = current_app.config.get('OLLAMA_HOST', '*************')
        self.ollama_port = current_app.config.get('OLLAMA_PORT', 11434)
        self.ollama_model = current_app.config.get('OLLAMA_MODEL', 'gemma3:1b')
        self.supported_languages = current_app.config.get('SUPPORTED_LANGUAGES', ['en', 'fr', 'pt', 'de', 'es'])
    
    def _get_cached_translation(self, content, source_lang, target_lang):
        """Get translation from PostgreSQL cache"""
        try:
            from app.models.translation_cache import TranslationCache

            cached = TranslationCache.get_cached_translation(
                content=content,
                source_lang=source_lang,
                target_lang=target_lang
            )

            if cached:
                return cached.translated_content

            return None

        except Exception as e:
            logging.error(f"Error retrieving cached translation: {e}")
            return None

    def _cache_translation(self, content, source_lang, target_lang, translated_content, translation_time_ms=None):
        """Cache translation in PostgreSQL"""
        try:
            from app.models.translation_cache import TranslationCache

            TranslationCache.cache_translation(
                content=content,
                source_lang=source_lang,
                target_lang=target_lang,
                translated_content=translated_content,
                translation_time_ms=translation_time_ms
            )

        except Exception as e:
            logging.error(f"Error caching translation: {e}")
    
    def _call_ollama_api(self, prompt):
        """Call Ollama API for translation"""
        url = f"http://{self.ollama_host}:{self.ollama_port}/api/generate"
        
        payload = {
            "model": self.ollama_model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": 0.3,  # Lower temperature for more consistent translations
                "top_p": 0.9,
                "max_tokens": 500
            }
        }
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            return result.get('response', '').strip()
            
        except requests.exceptions.RequestException as e:
            logging.error(f"Ollama API error: {e}")
            return None
        except json.JSONDecodeError as e:
            logging.error(f"JSON decode error: {e}")
            return None
    
    def _create_translation_prompt(self, content, source_lang, target_lang, context=None):
        """Create a prompt for translation that preserves context"""
        
        lang_names = {
            'en': 'English',
            'fr': 'French',
            'pt': 'Portuguese',
            'de': 'German',
            'es': 'Spanish'
        }
        
        source_name = lang_names.get(source_lang, source_lang)
        target_name = lang_names.get(target_lang, target_lang)
        
        prompt = f"""You are a professional translator specializing in social media content. 
Translate the following {source_name} text to {target_name}, preserving:
- The original tone and style
- Hashtags (keep them as-is)
- @mentions (keep them as-is)
- Emojis and special characters
- Cultural context and meaning

"""
        
        if context:
            prompt += f"Context: This is part of a social media conversation. Previous context: {context}\n\n"
        
        prompt += f"Text to translate: {content}\n\nTranslation:"
        
        return prompt
    
    def translate_content(self, content, source_lang, target_lang, context=None):
        """
        Translate content from source language to target language
        
        Args:
            content (str): Content to translate
            source_lang (str): Source language code (e.g., 'en')
            target_lang (str): Target language code (e.g., 'fr')
            context (str, optional): Additional context for better translation
            
        Returns:
            dict: Translation result with success status and translated content
        """
        
        # Validate languages
        if source_lang not in self.supported_languages or target_lang not in self.supported_languages:
            return {
                'success': False,
                'error': 'Unsupported language',
                'translated_content': content
            }
        
        # If source and target are the same, return original
        if source_lang == target_lang:
            return {
                'success': True,
                'translated_content': content,
                'cached': False
            }
        
        # Check cache first
        cached_translation = self._get_cached_translation(content, source_lang, target_lang)
        if cached_translation:
            return {
                'success': True,
                'translated_content': cached_translation,
                'cached': True
            }
        
        # Create translation prompt
        prompt = self._create_translation_prompt(content, source_lang, target_lang, context)

        # Call Ollama API with timing
        start_time = time.time()
        translated_content = self._call_ollama_api(prompt)
        end_time = time.time()
        translation_time_ms = int((end_time - start_time) * 1000)

        if translated_content:
            # Cache the translation with timing information
            self._cache_translation(content, source_lang, target_lang, translated_content, translation_time_ms)

            return {
                'success': True,
                'translated_content': translated_content,
                'cached': False,
                'translation_time_ms': translation_time_ms
            }
        else:
            return {
                'success': False,
                'error': 'Translation service unavailable',
                'translated_content': content
            }
    
    def translate_post(self, post, target_lang, context=None):
        """
        Translate a post object to target language
        
        Args:
            post: Post object with content and original_language
            target_lang (str): Target language code
            context (str, optional): Additional context
            
        Returns:
            dict: Post data with translated content
        """
        
        result = self.translate_content(
            content=post.content,
            source_lang=post.original_language,
            target_lang=target_lang,
            context=context
        )
        
        post_data = post.to_dict()
        post_data['translated_content'] = result['translated_content']
        post_data['translation_success'] = result['success']
        post_data['translation_cached'] = result.get('cached', False)
        post_data['target_language'] = target_lang
        
        if not result['success']:
            post_data['translation_error'] = result.get('error', 'Unknown error')
        
        return post_data
    
    def translate_message(self, message, target_lang):
        """
        Translate a message object to target language
        
        Args:
            message: Message object with content and original_language
            target_lang (str): Target language code
            
        Returns:
            dict: Message data with translated content
        """
        
        result = self.translate_content(
            content=message.content,
            source_lang=message.original_language,
            target_lang=target_lang
        )
        
        message_data = message.to_dict()
        message_data['translated_content'] = result['translated_content']
        message_data['translation_success'] = result['success']
        message_data['translation_cached'] = result.get('cached', False)
        message_data['target_language'] = target_lang
        
        if not result['success']:
            message_data['translation_error'] = result.get('error', 'Unknown error')
        
        return message_data
    
    def get_cache_stats(self):
        """Get translation cache statistics"""
        total_translations = self.translations_collection.count_documents({})
        
        # Get stats by language pair
        pipeline = [
            {
                '$group': {
                    '_id': {
                        'source_lang': '$source_lang',
                        'target_lang': '$target_lang'
                    },
                    'count': {'$sum': 1}
                }
            },
            {'$sort': {'count': -1}}
        ]
        
        language_pairs = list(self.translations_collection.aggregate(pipeline))
        
        return {
            'total_translations': total_translations,
            'language_pairs': language_pairs,
            'supported_languages': self.supported_languages
        }
    
    def cleanup_old_cache(self, days=30):
        """Clean up old cached translations"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        result = self.translations_collection.delete_many({
            'last_accessed': {'$lt': cutoff_date}
        })
        
        return result.deleted_count
